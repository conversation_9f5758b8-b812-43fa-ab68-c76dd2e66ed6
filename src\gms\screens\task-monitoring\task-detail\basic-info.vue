<template>
  <div class="tw-bg-background-light tw-p-4 tw-rounded-md tw-mb-4">
    <page-header />
    <div class="tw-flex tw-items-center tw-mb-3">
      <img class="tw-w-10 tw-h-10" :src="taskIcon" />
      <p class="tw-title tw-ml-2">
        {{ $t("geekplus.gms.client.screen.taskMonitoring.detail.basicInfo.taskId.label") }}:
      </p>
      <p class="tw-title tw-ml-2">
        {{ $props.data?.id || "-" }}
        <gp-tooltip
          v-if="$props.data?.id"
          placement="top-start"
          class="tw-ml-2"
          :content="$t('geekplus.gms.client.screen.taskMonitoring.detail.basicInfo.taskId.copy')"
        >
          <icon-base class="tw-cursor-pointer" @click="handleCopy($props.data?.id)">
            <icon-copy />
          </icon-base>
        </gp-tooltip>
      </p>
      <gp-tag
        v-if="$props.isHistoryTask && historyTaskStatus.includesValue($props.data.workflowStatus)"
        class="tw-ml-4"
        :type="historyTaskStatus.find({ value: $props.data.workflowStatus }, 'color')"
        >{{ historyTaskStatus.getLabelByValue($props.data.workflowStatus) }}</gp-tag
      >
      <gp-tag
        v-else-if="realTimeTaskStatus.includesValue($props.data.workflowStatus)"
        class="tw-ml-4"
        :type="realTimeTaskStatus.find({ value: getWorkflowStatus($props.data) }, 'color')"
        >{{ realTimeTaskStatus.getLabelByValue(getWorkflowStatus($props.data)) }}</gp-tag
      >
    </div>
    <div class="tw-flex tw-flex-row tw-flex-wrap">
      <div
        v-for="(item, index) in descriptions.filter((v) => !v.isHidden)"
        :key="index"
        class="tw-basis-1/3 tw-flex tw-mb-2 tw-text-sm"
      >
        <template v-if="item.type === 'redirect'">
          <p class="tw-w-28 tw-text-gray-400">{{ item.label }}：</p>
          <!-- <div>
            <gp-tooltip v-if="!!item.tooltip" placement="top-start" :content="item.tooltip">
              <p class="tw-flex-1 hover:tw-cursor-pointer tw-text-primary" @click="handleRedirect(item.path)">
                {{ item.value || "-" }}
              </p>
            </gp-tooltip>
            <p v-else class="tw-flex-1 hover:tw-cursor-pointer tw-text-primary" @click="handleRedirect(item.path)">
              {{ item.value || "-" }}
            </p>
          </div> -->
        </template>
        <template v-else-if="item.type === 'copy'">
          <p class="tw-w-28 tw-text-gray-400">{{ item.label }}：</p>
          <div class="tw-flex-1">
            <span class="tw-text-primary-text tw-mr-2">{{ item.value || "-" }}</span>
            <gp-tooltip
              v-if="item.value"
              :content="$t('geekplus.gms.client.screen.taskMonitoring.detail.basicInfo.taskId.copy')"
            >
              <icon-base class="tw-cursor-pointer" @click="handleCopy(item.value)">
                <icon-copy />
              </icon-base>
            </gp-tooltip>
          </div>
        </template>
        <template v-else>
          <p class="tw-w-28 tw-text-gray-400">{{ item.label }}：</p>
          <p class="tw-flex-1 tw-text-primary-text" :class="item.class">{{ item.value || "-" }}</p>
        </template>
      </div>
    </div>
    <sub-workflows v-if="$props.subflows?.length" :data="$props.subflows" />
    <parent-workflow v-if="$props.parentWorkflowId" :id="$props.parentWorkflowId" :loop="$props.loop" />
  </div>
</template>

<script>
import { defineComponent, computed } from "vue";
import { useI18n } from "gms-hooks/use-i18n";
import IconBase from "gms-components/icon-base";
import IconCopy from "gms-components/icons/icon-copy";
import copy from "copy-to-clipboard";
import { Message } from "geekplus-ui";
import { useRouter } from "vue-router/composables";
import { formatTime } from "gms-utils";
import {
  realTimeTaskCurrentStage,
  historyTaskStatus,
  realTimeTaskStatus,
  taskCloseModeEnum,
  taskSource,
  locationTypeOpts,
} from "gms-constants";
import { displayLocation, getWorkflowStatus, getErrorDescription } from "@/gms/screens/task-monitoring/common";
import PageHeader from "./page-header";
import taskIcon from "@/gms/assets/images/task-monitoring/task-all.png";
import ParentWorkflow from "./parent-workflow";
import SubWorkflows from "./sub-workflows";

export default defineComponent({
  name: "TaskBasicInfo",
  components: { IconBase, IconCopy, PageHeader, ParentWorkflow, SubWorkflows },
  props: {
    data: {
      type: Object,
      required: true,
    },
    isHistoryTask: {
      type: Boolean,
    },
    subflows: { type: Array, default: () => [] },
    parentWorkflowId: { type: [String, Number], default: "" },
    loop: { type: Object, default: () => ({}) },
  },
  setup(props) {
    const router = useRouter();
    const $t = useI18n();

    const realTimeDescriptions = computed(() => {
      return [
        {
          label: $t("geekplus.gms.client.screen.taskMonitoring.detail.basicInfo.taskName"),
          value: props?.data?.name,
          type: props?.data?.workflowDefinitionKey && "redirect",
          path: props?.data?.workflowDefinitionKey && getWorkflowPath(),
          tooltip: $t("geekplus.gms.client.screen.taskMonitoring.detail.basicInfo.taskName.value.tooltip"),
        },
        {
          label: $t("geekplus.gms.client.screen.taskMonitoring.detail.basicInfo.from"),
          value: taskSource.getLabelByValue(props?.data?.source),
        },
        {
          label: $t("geekplus.gms.client.screen.taskMonitoring.detail.basicInfo.priority"),
          value: props?.data?.priority,
        },
        {
          label: $t("geekplus.gms.client.screen.taskMonitoring.detail.basicInfo.start"),
          value: displayLocation({
            locationCode: props?.data?.startLocation?.locationCode,
            locationType: props?.data?.startLocation?.locationType,
            currentLocationText: $t("geekplus.gms.client.screen.taskMonitoring.startLocationCode.-1"),
          }),
        },
        {
          label: $t("geekplus.gms.client.screen.taskMonitoring.detail.basicInfo.upstreamTaskNum"),
          value: props?.data?.taskCode,
          type: "copy",
        },
        {
          label: $t("geekplus.gms.client.screen.taskMonitoring.detail.basicInfo.robotTaskID"),
          value: props?.data?.robotState?.robotTaskId,
          type: "copy",
        },
        {
          label: $t("geekplus.gms.client.screen.taskMonitoring.detail.basicInfo.end"),
          value: props?.data?.endLocation?.locationCode
            ? `${locationTypeOpts.getLabelByValue(props?.data?.endLocation?.locationType)}${
                props?.data?.endLocation?.locationCode
              }`
            : "-",
        },
        {
          label: $t("geekplus.gms.client.screen.taskMonitoring.detail.basicInfo.createTime"),
          value: formatTime(props?.data?.createTime),
        },
        {
          label: $t("geekplus.gms.client.screen.taskMonitoring.detail.basicInfo.currentStage"),
          value: realTimeTaskCurrentStage.getLabelByValue(props?.data?.activityState),
        },
        {
          label: $t("geekplus.gms.client.screen.taskMonitoring.detail.basicInfo.exceptionDesc"),
          value: getErrorDescription(props?.data?.error),
          class: "tw-text-red-500",
          isHidden: !props?.data?.error,
        },
      ];
    });

    const historyDescriptions = computed(() => {
      return [
        {
          label: $t("geekplus.gms.client.screen.taskMonitoring.detail.basicInfo.taskName"),
          value: props?.data?.name,
          type: props?.data?.workflowDefinitionKey && "redirect",
          path: props?.data?.workflowDefinitionKey && getWorkflowPath(),
          tooltip: $t("geekplus.gms.client.screen.taskMonitoring.detail.basicInfo.taskName.value.tooltip"),
        },
        {
          label: $t("geekplus.gms.client.screen.taskMonitoring.detail.basicInfo.from"),
          value: taskSource.getLabelByValue(props?.data?.source),
        },
        {
          label: $t("geekplus.gms.client.screen.taskMonitoring.detail.basicInfo.creator"),
          value: props?.data?.createdBy?.screenName,
        },
        {
          label: $t("geekplus.gms.client.screen.taskMonitoring.detail.basicInfo.start"),
          value: displayLocation({
            locationCode: props?.data?.startLocation?.locationCode,
            locationType: props?.data?.startLocation?.locationType,
            currentLocationText: $t("geekplus.gms.client.screen.taskMonitoring.startLocationCode.-1"),
          }),
        },
        {
          label: $t("geekplus.gms.client.screen.taskMonitoring.detail.basicInfo.upstreamTaskNum"),
          value: props?.data?.taskCode,
          type: "copy",
        },
        {
          label: $t("geekplus.gms.client.screen.taskMonitoring.detail.basicInfo.createTime"),
          value: formatTime(props?.data?.createTime),
        },
        {
          label: $t("geekplus.gms.client.screen.taskMonitoring.detail.basicInfo.end"),
          value: props?.data?.endLocation?.locationCode
            ? `${locationTypeOpts.getLabelByValue(props?.data?.endLocation?.locationType)}${
                props?.data?.endLocation?.locationCode
              }`
            : "-",
        },
        {
          label: $t("geekplus.gms.client.screen.taskMonitoring.detail.basicInfo.robotTaskID"),
          value: props?.data?.robotState?.robotTaskId,
          type: "copy",
        },
        {
          label: $t("geekplus.gms.client.screen.taskMonitoring.detail.basicInfo.completedTime"),
          value: formatTime(props?.data?.completedTime),
        },
        {
          label: $t("geekplus.gms.client.screen.taskMonitoring.detail.basicInfo.workflowEndMethod"),
          value: taskCloseModeEnum.getLabelByValue(props?.data?.workflowEndMethod) ?? "--",
        },
      ];
    });

    const getWorkflowPath = () => {
      let url = "/flowConfig";
      if (props.data.workflowDefinitionSource === "WORKFLOW_TEMPLATE") {
        url = "/flowTemplate";
      }

      return `${url}?workflowCodeAsFuzz=${props?.data?.workflowDefinitionKey}&mode=detail`;
    };

    const descriptions = computed(() => {
      if (props.isHistoryTask) {
        return historyDescriptions.value;
      }

      return realTimeDescriptions.value;
    });

    const handleCopy = (value) => {
      if (!value) {
        return;
      }

      copy(value);

      Message.success($t("geekplus.gms.client.screen.taskMonitoring.detail.basicInfo.copySuccess"));
    };

    const handleRedirect = (path) => {
      window.open(router.resolve(path).href);
    };

    return {
      $t,
      handleCopy,
      handleRedirect,
      descriptions,
      historyTaskStatus,
      realTimeTaskStatus,
      locationTypeOpts,
      taskIcon,
      getWorkflowStatus,
    };
  },
});
</script>
